/**
 * SEO Configuration Module
 * 
 * Centralized SEO configuration following the filestructure-first principle.
 * This module provides SEO-specific configuration that extends the main site config.
 */

import { SITE_CONFIG } from '@/lib/constants/site';

/**
 * SEO-specific configuration
 * Extends the main site configuration with SEO-focused settings
 */
export const SEO_CONFIG = {
  // Language and locale settings
  hreflang: 'nb-NO',
  defaultLocale: 'nb_NO',
  alternateLocales: ['nb-NO'],
  
  // Canonical URL configuration
  canonicalBaseUrl: SITE_CONFIG.url,
  
  // Sitemap configuration
  sitemap: {
    enabled: true,
    path: '/sitemap.xml',
    changefreq: 'weekly' as const,
    priority: {
      home: 1.0,
      main_pages: 0.8,
      service_pages: 0.7,
      project_pages: 0.6,
      detail_pages: 0.5
    }
  },
  
  // Robots.txt configuration
  robots: {
    crawlDelay: 10,
    allowedPaths: [
      '/hvem',
      '/hva',
      '/kontakt',
      '/tjenester/',
      '/prosjekter/',
      '/kundehistorier',
      // Local area pages following established SEO patterns
      '/hole',      // Hole kommune
      '/ringerike', // Ringerike (hovedområde)
      '/royse',     // Røyse (hovedkontor)
      '/honefoss'   // Hønefoss (tettsted)
    ],
    disallowedPaths: [
      '/admin/',
      '/system/',
      '/temp/',
      '/cache/',
      '/meta/' // Keep meta utilities private
    ]
  },
  
  // Schema.org configuration
  schema: {
    organization: {
      type: 'LocalBusiness',
      subType: 'LandscapingBusiness'
    },
    breadcrumbs: {
      enabled: true,
      includeHome: true
    },
    faq: {
      enabled: true,
      maxQuestions: 5
    }
  },
  
  // Performance and optimization
  performance: {
    lazyLoadImages: true,
    preloadCriticalImages: true,
    generateWebP: true
  }
} as const;

/**
 * Get canonical URL for a given path
 * @param path - The path to generate canonical URL for
 * @returns Full canonical URL
 */
export const getCanonicalUrl = (path: string): string => {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${SEO_CONFIG.canonicalBaseUrl}${cleanPath}`;
};

/**
 * Get hreflang attributes for a page
 * @param path - The current page path
 * @returns Array of hreflang link objects
 */
export const getHreflangLinks = (path: string) => {
  const canonicalUrl = getCanonicalUrl(path);
  
  return [
    {
      rel: 'alternate',
      hreflang: SEO_CONFIG.hreflang,
      href: canonicalUrl
    },
    {
      rel: 'alternate', 
      hreflang: 'x-default',
      href: canonicalUrl
    }
  ];
};

/**
 * Check if SEO feature is enabled
 * @param feature - Feature name to check
 * @returns True if feature is enabled
 */
export const isSEOFeatureEnabled = (feature: keyof typeof SEO_CONFIG): boolean => {
  const value = SEO_CONFIG[feature];
  if (typeof value === 'boolean') return value;
  if (typeof value === 'object' && value !== null && 'enabled' in value) {
    return Boolean(value.enabled);
  }
  return true; // Default to enabled
};
