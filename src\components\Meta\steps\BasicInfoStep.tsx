import { <PERSON>, But<PERSON> } from '@/ui';
import { Input, DateInput, Select, MaskedInput, Textarea } from '@/ui/Form';
import {
  ContractStepProps,
  positionOptions,
  probationMonthsOptions
} from '@/lib/meta/types';
import {
  createFormFieldUpdater,
  handleTemporaryEmploymentChange,
  handleProbationPeriodChange,
  handleOwnToolsChange
} from '@/lib/meta/utils/form-helpers';
import { ArrowRight, User, Briefcase, Calendar } from 'lucide-react';

const BasicInfoStep = ({ formData, updateFormData, onNext }: ContractStepProps) => {
  // Use centralized form field updater
  const updateField = createFormFieldUpdater(updateFormData);

  const handleNextClick = () => {
    if (onNext) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      {/* Personal Information */}
      <Card title="Personopplysninger" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <User className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Grunnleggende informasjon</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Fullt navn"
              name="employeeName"
              value={formData.employeeName}
              onChange={(e) => updateField('employeeName', e.target.value)}
              placeholder="Ola Nordmann"
              required
            />

            <Input
              label="Adresse"
              name="employeeAddress"
              value={formData.employeeAddress}
              onChange={(e) => updateField('employeeAddress', e.target.value)}
              placeholder="Gateadresse, postnummer og sted"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DateInput
              label="Fødselsdato"
              name="employeeBirthDate"
              value={formData.employeeBirthDate}
              onChange={(e) => updateField('employeeBirthDate', e.target.value)}
              required
            />

            <DateInput
              label="Startdato"
              name="startDate"
              value={formData.startDate}
              onChange={(e) => updateField('startDate', e.target.value)}
              required
            />
          </div>
        </div>
      </Card>

      {/* Position Information */}
      <Card title="Stillingsinformasjon" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Briefcase className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Stilling og lønn</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="Stillingstittel"
              name="position"
              options={positionOptions}
              value={formData.position}
              onChange={(e) => updateField('position', e.target.value)}
              required
            />

            <Input
              label="Timelønn (kr)"
              name="hourlyRate"
              type="number"
              value={formData.hourlyRate}
              onChange={(e) => updateField('hourlyRate', Number(e.target.value))}
              placeholder="300"
              required
            />
          </div>

          <Textarea
            label="Arbeidsoppgaver (stillingsbeskrivelse)"
            name="positionDescription"
            value={formData.positionDescription}
            onChange={(e) => updateField('positionDescription', e.target.value)}
            rows={3}
            placeholder="Beskriv hovedarbeidsoppgaver, ansvarsområder og eventuelle spesielle krav til stillingen"
            required
          />

          <MaskedInput
            label="Kontonummer"
            name="accountNumber"
            value={formData.accountNumber}
            onChange={(e) => updateField('accountNumber', e.target.value)}
            maskType="accountNumber"
            required
          />
        </div>
      </Card>

      {/* Employment Terms */}
      <Card title="Ansettelsesvilkår" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Calendar className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Ansettelsestype og vilkår</h3>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="temporaryEmployment"
              checked={formData.isTemporary}
              onChange={(e) => handleTemporaryEmploymentChange(e.target.checked, updateFormData)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="temporaryEmployment" className="text-sm font-medium text-gray-700">
              Midlertidig ansettelse
            </label>
          </div>

          <div className="text-sm text-gray-600">
            <strong>Ansettelsestype:</strong> {formData.isTemporary ? 'Midlertidig ansettelse' : 'Fast ansettelse'}
          </div>

          {formData.isTemporary && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <DateInput
                label="Sluttdato for midlertidig ansettelse"
                value={formData.temporaryEndDate}
                onChange={(e) => updateField('temporaryEndDate', e.target.value)}
              />

              <div className="md:col-span-2">
                <Textarea
                  label="Begrunnelse for midlertidig ansettelse"
                  value={formData.temporaryReason}
                  onChange={(e) => updateField('temporaryReason', e.target.value)}
                  placeholder="F.eks. vikariat for fast ansatt, sesongarbeid, tidsavgrenset prosjekt"
                  rows={3}
                />
              </div>
            </div>
          )}

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="probationPeriod"
              checked={formData.probationPeriod}
              onChange={(e) => handleProbationPeriodChange(e.target.checked, updateFormData)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="probationPeriod" className="text-sm font-medium text-gray-700">
              Prøvetid
            </label>
          </div>

          {formData.probationPeriod && (
            <div className="ml-7">
              <Select
                label="Lengde på prøvetid"
                options={probationMonthsOptions}
                value={formData.probationMonths.toString()}
                onChange={(e) => updateField('probationMonths', Number(e.target.value))}
              />
            </div>
          )}

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="ownTools"
              checked={formData.ownTools}
              onChange={(e) => handleOwnToolsChange(e.target.checked, updateFormData)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="ownTools" className="text-sm font-medium text-gray-700">
              Ansatt skal bruke eget verktøy
            </label>
          </div>
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end">
        <Button
          onClick={handleNextClick}
          variant="primary"
          size="lg"
        >
          Neste steg
          <ArrowRight className="h-5 w-5 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default BasicInfoStep;
