/**
 * Contact Information Constants
 *
 * This file contains all constants related to contact information.
 * It serves as a single source of truth for contact details throughout the application.
 *
 * SEO Considerations:
 * - Structured for schema.org LocalBusiness markup
 * - NAP (Name, Address, Phone) consistency for local SEO
 * - Geo-coordinates for map integration and local search
 * - Service areas with proper geographic targeting
 * - Business categories for industry-specific search relevance
 * - Opening hours in structured format for Google My Business compatibility
 */

/**
 * Main contact information object
 * Single source of truth for all contact information
 */
export const CONTACT_INFO = {
  phone: '+47 902 14 153',
  email: '<EMAIL>',
  address: {
    street: 'Birchs vei 7',
    postalCode: '3530',
    city: 'Røyse',
    municipality: 'Hole',
    county: 'Buskerud',
    country: 'Norge'
  },
  social: {
    facebook: 'https://www.facebook.com/RingerikeLandskap.no'
  },
  openingHours: {
    weekdays: '07:00-16:00',
    weekend: 'Stengt'
  },

  // Additional contact details with SEO considerations - enhanced for local focus
  company: {
    name: 'Ringerike Landskap AS',
    orgNumber: '*********',
    vatNumber: 'NO*********MVA',
    foundedYear: 2020,
    // Business categories for schema.org and industry-specific search
    categories: [
      {
        id: 'landscaping',
        name: 'Anleggsgartner',
        schemaType: 'LandscapingBusiness',
        googleCategory: 'Landscaper'
      },
      {
        id: 'construction',
        name: 'Maskinentreprenør',
        schemaType: 'GeneralContractor',
        googleCategory: 'Construction Company'
      }
    ],
    // Price range for schema.org and Google My Business
    priceRange: '$$',
    // Business description optimized with keywords and local focus
    description: 'Profesjonell anleggsgartner og maskinentreprenør med base på Røyse og spesialkompetanse på lokale forhold i Ringerike-regionen. Vi skaper skreddersydde uterom tilpasset lokalt klima og terreng, med personlig oppfølging fra våre to dedikerte eiere som er direkte involvert i hvert prosjekt.',
    // Extended description with more local keywords
    extendedDescription: 'Ringerike Landskap AS er et ungt, engasjert og voksende anleggsgartnerfirma og maskinentreprenør med base i Hole kommune. Med inngående kjennskap til Ringerike-regionens unike terreng og jordforhold, skaper vi funksjonelle og estetiske uterom som tåler lokale klimaforhold. Våre tjenester inkluderer belegningsstein, støttemurer, ferdigplen, kantstein, cortenstål, platting, trapper og beplantning – alle tilpasset lokale forhold fra Røyse til Hønefoss, Jevnaker, Sundvollen og Vik.',
    // Year established for trust signals in schema.org
    yearEstablished: '2020',
    // Payment methods for schema.org
    paymentAccepted: ['Vipps', 'Bankoverføring', 'Faktura'],
    // Languages for visitors
    languages: ['Norwegian', 'English'],
    // Owners information for personal touch
    owners: [
      {
        name: 'Kim Tuvsjøen',
        expertise: ['Anleggsgartner', 'Maskinentreprenør', 'Prosjektledelse'],
        description: 'Medgründer med omfattende erfaring innen anleggsgartnerfaget og maskinentreprenørarbeid i Ringerike-regionen.'
      },
      {
        name: 'Jan Iversen',
        expertise: ['Anleggsgartner', 'Sveising', 'Cortenstål'],
        description: 'Medgründer med spesialkompetanse på sveising og kreative løsninger med cortenstål, samt solid erfaring som anleggsgartner i lokalmiljøet.'
      }
    ],
    // Local focus for SEO
    serviceRadius: '50km',
    mainServiceArea: 'Ringerike-regionen',
    counties: ['Buskerud', 'Viken'],
    municipalities: ['Hole', 'Ringerike', 'Jevnaker']
  },

  // Team members with contact information
  team: [
    {
      id: 'kim',
      name: 'Kim Tuvsjøen',
      title: 'Anleggsgartner',
      phone: '+47 902 14 153',
      phoneRaw: '+4790214153',
      email: '<EMAIL>',
      image: '/images/team/ringerikelandskap-kim.webp',
      isMainContact: true
    },
    {
      id: 'jan',
      name: 'Jan Iversen',
      title: 'Anleggsgartner',
      phone: '+47 990 30 341',
      phoneRaw: '+4799030341',
      email: '<EMAIL>',
      image: '/images/team/ringerikelandskap-jan.webp',
      isMainContact: false
    }
  ],

  // Form defaults
  form: {
    defaultSubject: 'Forespørsel om befaring',
    defaultMessage: 'Jeg ønsker en uforpliktende befaring for...',
    successMessage: 'Takk for din henvendelse. Vi har mottatt din melding og vil ta kontakt med deg så snart som mulig.',
    errorMessage: 'Det oppstod en feil ved sending av skjemaet. Vennligst prøv igjen eller kontakt oss direkte på telefon.'
  },

  // Service areas with SEO-optimized descriptions targeting the specific 20-50km radius
  serviceAreas: [
    {
      id: 'royse',
      name: 'Røyse',
      postalCodes: ['3530'],
      radius: 5, // km
      priority: 1, // Highest priority (base location)
      // SEO-optimized description with location keywords
      description: 'Med base på Røyse har vi inngående kjennskap til lokale forhold og grunnforhold. Vi tilbyr skreddersydde løsninger som tåler det lokale klimaet og fremhever din eiendoms unike kvaliteter.',
      // Main services for this area (for location pages)
      primaryServices: ['Belegningsstein', 'Cortenstål', 'Steinmur'],
      // Schema.org GeoShape for service area
      geoShape: {
        type: 'Circle',
        coordinates: [60.0584, 10.2551], // Røyse center
        radius: '5km'
      },
      // Local landmarks for SEO content
      landmarks: ['Tyrifjorden', 'Røyse skole', 'Birchs vei'],
      // Terrain characteristics for targeted content
      terrainFeatures: ['Nærhet til Tyrifjorden', 'Variert terreng', 'Leirholdig jord']
    },
    {
      id: 'hole',
      name: 'Hole',
      postalCodes: ['3530', '3531', '3532'],
      radius: 10, // km
      priority: 2, // High priority (home municipality)
      description: 'Som lokalt anleggsgartnerfirma med base i Hole kommune har vi inngående kjennskap til lokale forhold. Vi leverer kvalitetstjenester tilpasset Holes varierte terreng og nærhet til Tyrifjorden.',
      primaryServices: ['Belegningsstein', 'Ferdigplen', 'Platting'],
      geoShape: {
        type: 'Circle',
        coordinates: [60.0584, 10.3051], // Hole center
        radius: '10km'
      },
      landmarks: ['Hole kirke', 'Sundvollen', 'Vik'],
      terrainFeatures: ['Nærhet til Tyrifjorden', 'Variert terreng', 'Leirholdig jord']
    },
    {
      id: 'honefoss',
      name: 'Hønefoss',
      postalCodes: ['3510', '3511', '3512', '3513', '3514', '3515'],
      radius: 15, // km
      priority: 2, // High priority (major population center)
      description: 'Vi tilbyr profesjonelle anleggsgartnertjenester i hele Hønefoss-området, med spesialkompetanse på lokale grunnforhold og klima. Med kort reisevei fra vår base sikrer vi effektiv gjennomføring av prosjekter.',
      primaryServices: ['Steinmur', 'Belegningsstein', 'Platting'],
      geoShape: {
        type: 'Circle',
        coordinates: [60.1699, 10.2557], // Hønefoss center
        radius: '15km'
      },
      landmarks: ['Hønefoss sentrum', 'Schjongslunden', 'Petersøya'],
      terrainFeatures: ['Kupert terreng', 'Elvelandskap', 'Varierte grunnforhold']
    },
    {
      id: 'jevnaker',
      name: 'Jevnaker',
      postalCodes: ['3520', '3521'],
      radius: 20, // km
      priority: 3, // Medium priority
      description: 'Ringerike Landskap leverer kvalitetsløsninger for uterom i Jevnaker kommune. Med vår erfaring fra regionen skaper vi holdbare og estetiske uteområder tilpasset lokale forhold. Vi tilbyr tjenester som støttemurer, belegningsstein, platting og trapper som fremhever din eiendoms potensial i Jevnakers unike landskap.',
      primaryServices: ['Støttemur', 'Belegningsstein', 'Platting', 'Trapp/repo'],
      geoShape: {
        type: 'Circle',
        coordinates: [60.2384, 10.3907], // Jevnaker center
        radius: '20km'
      },
      landmarks: ['Hadeland Glassverk', 'Kistefos Museum', 'Jevnaker kirke'],
      terrainFeatures: ['Skogsterreng', 'Randsfjorden', 'Kupert landskap']
    },
    {
      id: 'sundvollen',
      name: 'Sundvollen',
      postalCodes: ['3531'],
      radius: 8, // km
      priority: 3, // Medium priority
      description: 'Med kort avstand fra vår base i Røyse, tilbyr vi skreddersydde anleggsgartnertjenester i Sundvollen. Vår lokalkunnskap om grunnforhold og terreng sikrer at vi leverer holdbare og estetiske løsninger for belegningsstein, støttemurer, ferdigplen og hekk/beplantning som tåler klimaet ved Tyrifjorden og fremhever din eiendoms unike kvaliteter.',
      primaryServices: ['Belegningsstein', 'Støttemur', 'Ferdigplen', 'Hekk/beplantning'],
      geoShape: {
        type: 'Circle',
        coordinates: [60.0580, 10.3079], // Sundvollen center
        radius: '8km'
      },
      landmarks: ['Sundvollen Hotel', 'Krokkleiva', 'Tyrifjorden'],
      terrainFeatures: ['Bratt terreng', 'Nærhet til Tyrifjorden', 'Fjellgrunn']
    },
    {
      id: 'vik',
      name: 'Vik',
      postalCodes: ['3530'],
      radius: 7, // km
      priority: 3, // Medium priority
      description: 'Ringerike Landskap tilbyr lokaltilpassede anleggsgartnertjenester i Vik. Med vår base i nærliggende Røyse har vi inngående kjennskap til lokale forhold og leverer kvalitetsløsninger innen belegningsstein, ferdigplen, kantstein og hekk/beplantning som er skreddersydd for Viks unike beliggenhet ved Tyrifjorden.',
      primaryServices: ['Belegningsstein', 'Ferdigplen', 'Kantstein', 'Hekk/beplantning'],
      geoShape: {
        type: 'Circle',
        coordinates: [60.0789, 10.2890], // Vik center
        radius: '7km'
      },
      landmarks: ['Vik sentrum', 'Vik skole', 'Tyrifjorden'],
      terrainFeatures: ['Nærhet til Tyrifjorden', 'Leirholdig jord', 'Flatt terreng']
    }
  ],

  // Customer reviews for schema.org and trust signals - with location-specific mentions
  reviews: [
    {
      author: 'Kari Nordmann',
      location: 'Hønefoss',
      rating: 5,
      datePublished: '2023-06-15',
      reviewBody: 'Fantastisk arbeid med vår nye hage i Hønefoss. Profesjonelle, punktlige og løsningsorienterte. De to eierne var personlig involvert i hele prosessen og kom med kreative løsninger tilpasset vår tomt. Anbefales på det sterkeste!',
      // For schema.org
      itemReviewed: {
        type: 'Service',
        name: 'Hagedesign og belegningsstein'
      }
    },
    {
      author: 'Ola Hansen',
      location: 'Røyse',
      rating: 5,
      datePublished: '2023-08-22',
      reviewBody: 'Ringerike Landskap bygget en støttemur for oss på Røyse som har fått mange komplimenter. Solid håndverk og god kommunikasjon gjennom hele prosessen. Eierne kom med gode råd om hvordan muren best kunne tilpasses terrenget vårt.',
      itemReviewed: {
        type: 'Service',
        name: 'Støttemur'
      }
    },
    {
      author: 'Marte Kirkerud',
      location: 'Vik',
      rating: 5,
      datePublished: '2023-07-10',
      reviewBody: 'Vi fikk lagt belegningsstein og plantet hekk rundt hele huset i Vik. Resultatet ble over all forventning! Guttene i Ringerike Landskap er utrolig dyktige og løsningsorienterte. De foreslo løsninger vi ikke hadde tenkt på selv, som virkelig løftet prosjektet.',
      itemReviewed: {
        type: 'Service',
        name: 'Belegningsstein og hekk/beplantning'
      }
    },
    {
      author: 'Johan Pedersen',
      location: 'Sundvollen',
      rating: 5,
      datePublished: '2023-05-18',
      reviewBody: 'Ringerike Landskap har anlagt en helt ny hage for oss i Sundvollen, med terrasse, støttemur og beplantning. Eierne er utrolig kunnskapsrike og engasjerte, og resultatet ble fantastisk! De har virkelig peiling på lokale forhold og hva som fungerer i vårt klima.',
      itemReviewed: {
        type: 'Service',
        name: 'Komplett hageanlegg'
      }
    }
  ],

  // Core services with SEO-optimized descriptions
  coreServices: [
    {
      id: 'kantstein',
      name: 'Kantstein',
      description: 'Profesjonell legging av kantstein som skaper rene linjer og definerer ulike soner i hagen. Våre kantsteinsløsninger er tilpasset lokale forhold i Ringerike-regionen og tåler frost og tele.',
      benefits: [
        'Definerer bed og gangarealer',
        'Forenkler vedlikehold',
        'Øker eiendommens verdi',
        'Frostsikker installasjon'
      ],
      popularIn: ['Hole', 'Hønefoss', 'Vik']
    },
    {
      id: 'ferdigplen',
      name: 'Ferdigplen',
      description: 'Rask og effektiv etablering av plen med ferdiggress tilpasset klimaet i Ringerike-regionen. Vi forbereder grunnen grundig for å sikre god drenering og vekstforhold.',
      benefits: [
        'Umiddelbart grønt resultat',
        'Nøye utvalgt gresstype for lokalt klima',
        'Profesjonell grunnarbeid',
        'Reduserer erosjon'
      ],
      popularIn: ['Røyse', 'Hole', 'Vik', 'Sundvollen']
    },
    {
      id: 'stottemur',
      name: 'Støttemur',
      description: 'Solide støttemurer bygget med fokus på holdbarhet i det norske klimaet. Vi har spesialkompetanse på støttemurer i skrånende terreng, typisk for mange tomter i Ringerike-området.',
      benefits: [
        'Skaper flere brukbare flater i kupert terreng',
        'Hindrer erosjon og utglidning',
        'Estetisk tiltalende løsninger',
        'Bygget for norske forhold'
      ],
      popularIn: ['Hønefoss', 'Jevnaker', 'Sundvollen']
    },
    {
      id: 'hekk-beplantning',
      name: 'Hekk/beplantning',
      description: 'Profesjonell planting av hekk og annen beplantning tilpasset lokale vekstforhold i Ringerike-regionen. Vi velger planter som trives i lokalt klima og jord.',
      benefits: [
        'Skaper le og skjerming',
        'Øker biologisk mangfold',
        'Tilpasset lokalt klima',
        'Reduserer støy og innsyn'
      ],
      popularIn: ['Røyse', 'Hole', 'Vik', 'Sundvollen']
    },
    {
      id: 'cortenstal',
      name: 'Cortenstål',
      description: 'Unike løsninger med cortenstål, designet og installert av våre fagfolk med spesialkompetanse på sveising. Perfekt for å skape moderne, varige elementer i hagen.',
      benefits: [
        'Moderne uttrykk',
        'Vedlikeholdsfritt',
        'Lang levetid',
        'Skreddersydde løsninger'
      ],
      popularIn: ['Hønefoss', 'Jevnaker']
    },
    {
      id: 'belegningsstein',
      name: 'Belegningsstein',
      description: 'Profesjonell legging av belegningsstein med fokus på holdbarhet i det norske klimaet. Vi har lang erfaring med ulike typer belegningsstein og mønsterlegging.',
      benefits: [
        'Frostsikker installasjon',
        'Mange design- og fargealternativer',
        'Øker eiendommens verdi',
        'Minimalt vedlikehold'
      ],
      popularIn: ['Røyse', 'Hole', 'Hønefoss', 'Jevnaker', 'Sundvollen', 'Vik']
    },
    {
      id: 'platting',
      name: 'Platting',
      description: 'Skreddersydde plattinger i tre eller komposittmateriale, designet for å tåle det norske klimaet. Vi bygger plattinger som skaper perfekte uterom for rekreasjon.',
      benefits: [
        'Utvider boligens bruksareal',
        'Tilpasset tomtens terreng',
        'Velg mellom ulike materialer',
        'Bygget for norske forhold'
      ],
      popularIn: ['Hønefoss', 'Jevnaker']
    },
    {
      id: 'trapp-repo',
      name: 'Trapp/repo',
      description: 'Funksjonelle og estetiske trappeløsninger tilpasset terrenget på din eiendom. Vi har spesialkompetanse på trapper i skrånende terreng, typisk for mange tomter i Ringerike-området.',
      benefits: [
        'Sikker adkomst i kupert terreng',
        'Skreddersydd design',
        'Holdbare materialer',
        'Øker tilgjengeligheten i hagen'
      ],
      popularIn: ['Hønefoss', 'Jevnaker', 'Sundvollen']
    }
  ]
};

/**
 * Helper functions for contact information
 */

/**
 * Get a phone number link with the tel: protocol
 */
export const getPhoneLink = (teamMemberId?: string) => {
  if (teamMemberId) {
    const teamMember = CONTACT_INFO.team.find(member => member.id === teamMemberId);
    return teamMember ? `tel:${teamMember.phoneRaw}` : `tel:${CONTACT_INFO.phone.replace(/\s/g, '')}`;
  }
  return `tel:${CONTACT_INFO.phone.replace(/\s/g, '')}`;
};

/**
 * Get an email link with the mailto: protocol
 */
export const getEmailLink = (teamMemberId?: string) => {
  if (teamMemberId) {
    const teamMember = CONTACT_INFO.team.find(member => member.id === teamMemberId);
    return teamMember ? `mailto:${teamMember.email}` : `mailto:${CONTACT_INFO.email}`;
  }
  return `mailto:${CONTACT_INFO.email}`;
};

/**
 * Get the full address as a formatted string
 */
export const getFullAddress = () => {
  const { street, postalCode, city } = CONTACT_INFO.address;
  return `${street}, ${postalCode} ${city}`;
};

/**
 * Get a Google Maps URL for the address
 */
export const getGoogleMapsUrl = () => {
  // Variables removed as they're not needed
  return `https://g.co/kgs/2gMYeog`;
};

/**
 * Get the main contact person
 */
export const getMainContact = () => {
  return CONTACT_INFO.team.find(member => member.isMainContact) || CONTACT_INFO.team[0];
};

/**
 * Get the company name with the current year for copyright notices
 */
export const getCopyrightText = () => {
  const currentYear = new Date().getFullYear();
  return `© ${currentYear} ${CONTACT_INFO.company.name}. Org.nr: ${CONTACT_INFO.company.orgNumber}`;
};

/**
 * Get opening hours as a formatted string
 */
export const getFormattedOpeningHours = (day: 'weekdays' | 'weekend') => {
  return CONTACT_INFO.openingHours[day];
};

/**
 * Get schema.org structured data for the company
 * Enhanced for maximum SEO impact with LocalBusiness markup
 * Optimized for Ringerike region (20-50km radius from Røyse)
 *
 * @param {string} [serviceAreaId] - Optional service area ID to generate area-specific schema
 * @returns {object} Schema.org structured data object
 */
export const getCompanySchema = (serviceAreaId?: string) => {
  // Get the primary business category
  const primaryCategory = CONTACT_INFO.company.categories[0];

  // Get service area if specified
  const serviceArea = serviceAreaId
    ? CONTACT_INFO.serviceAreas.find((area) => area.id === serviceAreaId)
    : null;

  // Base schema that's common for all areas
  const baseSchema = {
    "@context": "https://schema.org",
    "@type": primaryCategory.schemaType,
    name: CONTACT_INFO.company.name,
    description: serviceArea?.description || CONTACT_INFO.company.description,
    telephone: CONTACT_INFO.phone,
    email: CONTACT_INFO.email,
    url: "https://ringerikelandskap.no",
    logo: "https://ringerikelandskap.no/images/logo.png",
    image: [
      "https://ringerikelandskap.no/images/hero/hero-home-main.webp",
      "https://ringerikelandskap.no/images/hero/hero-services-granite.webp"
    ],
    address: {
      "@type": "PostalAddress",
      streetAddress: CONTACT_INFO.address.street,
      addressLocality: CONTACT_INFO.address.city,
      postalCode: CONTACT_INFO.address.postalCode,
      addressRegion: CONTACT_INFO.address.county,
      addressCountry: "NO"
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: 60.0558,
      longitude: 10.2558
    },
    openingHoursSpecification: {
      "@type": "OpeningHoursSpecification",
      dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      opens: "07:00",
      closes: "16:00"
    },
    sameAs: [CONTACT_INFO.social.facebook],
    foundingDate: `${CONTACT_INFO.company.foundedYear}-01-01`,
    vatID: CONTACT_INFO.company.vatNumber,
    priceRange: CONTACT_INFO.company.priceRange,
    paymentAccepted: CONTACT_INFO.company.paymentAccepted,
    currenciesAccepted: "NOK",

    // Add owners for personal touch (important for local business)
    founder: CONTACT_INFO.company.owners.map(owner => ({
      "@type": "Person",
      name: owner.name,
      description: owner.description,
      knowsAbout: owner.expertise
    })),

    // Add core services as service offerings
    makesOffer: CONTACT_INFO.coreServices.map(service => ({
      "@type": "Offer",
      itemOffered: {
        "@type": "Service",
        name: service.name,
        description: service.description
      }
    })),

    // Add reviews with location mentions
    review: CONTACT_INFO.reviews.map(review => ({
      "@type": "Review",
      author: {
        "@type": "Person",
        name: review.author
      },
      datePublished: review.datePublished,
      reviewRating: {
        "@type": "Rating",
        ratingValue: review.rating,
        bestRating: "5"
      },
      reviewBody: review.reviewBody,
      itemReviewed: {
        "@type": "Service",
        name: review.itemReviewed.name
      }
    })),

    // Aggregate rating based on reviews
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: CONTACT_INFO.reviews.reduce((sum, review) => sum + review.rating, 0) / CONTACT_INFO.reviews.length,
      reviewCount: CONTACT_INFO.reviews.length,
      bestRating: "5"
    }
  };

  // Add service area specific information if a service area is specified
  if (serviceArea) {
    return {
      ...baseSchema,
      // Location-specific name for better local SEO
      name: `${CONTACT_INFO.company.name} - Anleggsgartner i ${serviceArea.name}`,
      // Location-specific description
      description: serviceArea.description,
      // Geo-specific service area
      areaServed: {
        "@type": "GeoCircle",
        geoMidpoint: {
          "@type": "GeoCoordinates",
          latitude: serviceArea.geoShape.coordinates[0],
          longitude: serviceArea.geoShape.coordinates[1]
        },
        geoRadius: serviceArea.geoShape.radius
      },
      // Add service area specific services
      makesOffer: serviceArea.primaryServices.map(serviceName => {
        // Find the full service details
        const serviceDetails = CONTACT_INFO.coreServices.find(s => s.name === serviceName) || {
          id: serviceName.toLowerCase().replace(/\//g, '-'),
          name: serviceName,
          description: `Profesjonell ${serviceName.toLowerCase()} tilpasset lokale forhold i ${serviceArea.name}.`,
          benefits: []
        };

        return {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: serviceDetails.name,
            description: serviceDetails.description,
            areaServed: {
              "@type": "City",
              name: serviceArea.name
            }
          }
        };
      }),
      // Add local landmarks for rich content
      mentions: serviceArea.landmarks.map(landmark => ({
        "@type": "Place",
        name: landmark
      }))
    };
  }

  // If no specific service area, add the main service area (Ringerike region)
  return {
    ...baseSchema,
    // Add missing critical fields for better SEO
    openingHours: [
      "Mo-Fr 07:00-16:00"
    ],
    openingHoursSpecification: [
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        opens: "07:00",
        closes: "16:00"
      }
    ],
    // Add service catalog for better visibility with local SEO focus
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Anleggsgartnertjenester i Ringerike",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Belegningsstein i Hole og Ringerike",
            description: "Profesjonell legging av belegningsstein tilpasset lokale forhold i ringeriksregionen"
          }
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Støttemurer Ringerike",
            description: "Bygging av støttemurer og terrengutjevning tilpasset lokalt terreng"
          }
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Hagedesign Hole kommune",
            description: "Skreddersydde hageløsninger tilpasset norske forhold og lokalt klima"
          }
        }
      ]
    },
    // Define the service area as the entire Ringerike region
    areaServed: [
      {
        "@type": "GeoCircle",
        geoMidpoint: {
          "@type": "GeoCoordinates",
          latitude: 60.0584, // Røyse center
          longitude: 10.2551
        },
        geoRadius: CONTACT_INFO.company.serviceRadius
      },
      // Also list all municipalities served
      ...CONTACT_INFO.company.municipalities.map(municipality => ({
        "@type": "AdministrativeArea",
        name: municipality
      }))
    ],
    // List all service areas by name
    serviceArea: CONTACT_INFO.serviceAreas
      .sort((a, b) => a.priority - b.priority) // Sort by priority
      .map(area => ({
        "@type": "City",
        name: area.name
      }))
  };
};

/**
 * Get location-specific SEO metadata optimized for Ringerike region
 *
 * @param {string} locationId - Location ID to generate metadata for
 * @returns {object} SEO metadata object with title, description, keywords and schema
 */
export const getLocationSeoMetadata = (locationId: string) => {
  const serviceArea = CONTACT_INFO.serviceAreas.find(area => area.id === locationId);

  // If no specific location is found, return general metadata for the Ringerike region
  if (!serviceArea) {
    return {
      title: `Anleggsgartner i Ringerike-regionen | ${CONTACT_INFO.company.name}`,
      description: CONTACT_INFO.company.extendedDescription,
      keywords: [
        'anleggsgartner ringerike',
        'anleggsgartner hole',
        'maskinentreprenør ringerike',
        'belegningsstein ringerike',
        'støttemur hole',
        'anleggsgartner røyse',
        'hagedesign hønefoss',
        'cortenstål ringerike',
        'ferdigplen hole'
      ],
      schema: getCompanySchema()
    };
  }

  // Generate location-specific keywords if serviceArea exists
  const locationKeywords = serviceArea ? [
    // Location-specific service keywords
    ...serviceArea.primaryServices.map(service => `${service.toLowerCase()} ${serviceArea.name.toLowerCase()}`),
    // General service keywords for this location
    `anleggsgartner ${serviceArea.name.toLowerCase()}`,
    `hagearbeid ${serviceArea.name.toLowerCase()}`,
    `maskinentreprenør ${serviceArea.name.toLowerCase()}`,
    // Terrain-specific keywords
    ...serviceArea.terrainFeatures.map(feature =>
      `anleggsgartner ${feature.toLowerCase().replace('nærhet til ', '')}`
    ),
    // Landmark-specific keywords
    ...serviceArea.landmarks.map(landmark =>
      `anleggsgartner nær ${landmark.toLowerCase()}`
    ),
    // Postal code specific keywords
    ...serviceArea.postalCodes.map(code =>
      `anleggsgartner ${code}`
    )
  ] : [];

  // Create a rich, location-specific description if serviceArea exists
  const richDescription = serviceArea
    ? `${serviceArea.description} Vi er din lokale anleggsgartner i ${serviceArea.name} med over ${new Date().getFullYear() - CONTACT_INFO.company.foundedYear} års erfaring i området. Våre tjenester inkluderer ${serviceArea.primaryServices.join(', ')} – alle tilpasset lokale forhold og utført av våre dedikerte eiere som personlig følger opp hvert prosjekt.`
    : CONTACT_INFO.company.description;

  return {
    // Location-specific title
    title: `Anleggsgartner i ${serviceArea.name} | ${CONTACT_INFO.company.name}`,
    // Rich, location-specific description
    description: richDescription,
    // Comprehensive location-specific keywords
    keywords: locationKeywords,
    // Location-specific schema
    schema: getCompanySchema(serviceArea.id),
    // Additional metadata for templates
    locationData: {
      name: serviceArea.name,
      services: serviceArea.primaryServices,
      terrainFeatures: serviceArea.terrainFeatures,
      landmarks: serviceArea.landmarks,
      nearbyAreas: CONTACT_INFO.serviceAreas
        .filter(area => area.id !== locationId &&
                        area.radius + serviceArea.radius >=
                        calculateDistance(
                          serviceArea.geoShape.coordinates[0],
                          serviceArea.geoShape.coordinates[1],
                          area.geoShape.coordinates[0],
                          area.geoShape.coordinates[1]
                        ))
        .map(area => area.name)
    }
  };
};

/**
 * Calculate distance between two points in kilometers
 * Using the Haversine formula
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  return distance;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI/180);
}
