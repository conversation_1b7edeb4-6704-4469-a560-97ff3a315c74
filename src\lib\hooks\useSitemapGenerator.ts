/**
 * Sitemap Generator Hook
 * 
 * Provides a function to generate sitemap on demand.
 * Separated from component to avoid fast refresh warnings.
 */

import { useData } from '@/lib/hooks';
import { getServices, getProjects } from '@/lib/api';
import { generateSitemap } from '@/lib/utils/sitemap';

/**
 * Hook for manual sitemap generation
 * Provides a function to generate sitemap on demand
 */
export const useSitemapGenerator = () => {
  const { data: services, loading: servicesLoading, error: servicesError } = useData(getServices);
  const { data: projects, loading: projectsLoading, error: projectsError } = useData(getProjects);
  
  const isLoading = servicesLoading || projectsLoading;
  const error = servicesError || projectsError;

  const generateSitemapManually = () => {
    if (isLoading || error || !services || !projects) {
      throw new Error('Data not ready for sitemap generation');
    }

    return generateSitemap({
      includeServices: true,
      includeProjects: true,
      includeLocationPages: true,
      excludePaths: ['/meta/']
    });
  };

  return {
    generateSitemap: generateSitemapManually,
    isReady: !isLoading && !error && services && projects,
    error
  };
};
