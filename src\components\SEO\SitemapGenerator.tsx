/**
 * Sitemap Generator Component
 * 
 * React component for generating sitemap at build time.
 * Follows existing component patterns in src/components/SEO/
 */

import { useEffect } from 'react';
import { useData } from '@/lib/hooks';
import { getServices, getProjects } from '@/lib/api';
import { generateSitemap } from '@/lib/utils/sitemap';
import { SEO_CONFIG } from '@/lib/config/seo';
import { debug } from '@/lib/config';

interface SitemapGeneratorProps {
  /**
   * Whether to generate sitemap automatically on mount
   */
  autoGenerate?: boolean;
  
  /**
   * Callback when sitemap is generated
   */
  onGenerated?: (sitemap: string) => void;
  
  /**
   * Whether to log generation details
   */
  verbose?: boolean;
}

/**
 * Sitemap Generator Component
 * 
 * Generates XML sitemap using existing data sources and routing structure.
 * Integrates with the existing data flow through useData hook.
 */
export const SitemapGenerator: React.FC<SitemapGeneratorProps> = ({
  autoGenerate = false,
  onGenerated,
  verbose = false
}) => {
  // Use existing data hook to ensure consistency with rest of application
  const { data: services, loading: servicesLoading, error: servicesError } = useData(getServices);
  const { data: projects, loading: projectsLoading, error: projectsError } = useData(getProjects);

  const isLoading = servicesLoading || projectsLoading;
  const error = servicesError || projectsError;

  useEffect(() => {
    // Only generate if auto-generation is enabled and data is loaded
    if (!autoGenerate || isLoading || error) {
      return;
    }

    // Ensure we have data before generating
    if (!services || !projects) {
      debug('SitemapGenerator: Waiting for data to load');
      return;
    }

    try {
      if (verbose) {
        debug('SitemapGenerator: Starting sitemap generation', {
          servicesCount: services.length,
          projectsCount: projects.length,
          seoConfig: SEO_CONFIG.sitemap
        });
      }

      // Generate sitemap using existing data
      const sitemap = generateSitemap({
        includeServices: true,
        includeProjects: true,
        includeLocationPages: true,
        excludePaths: ['/meta/'] // Exclude meta utilities from sitemap
      });

      if (verbose) {
        debug('SitemapGenerator: Sitemap generated successfully', {
          length: sitemap.length,
          urlCount: (sitemap.match(/<url>/g) || []).length
        });
      }

      // Call callback if provided
      if (onGenerated) {
        onGenerated(sitemap);
      }

      // In development, log the sitemap for debugging
      if (import.meta.env.DEV && verbose) {
        console.log('Generated Sitemap:', sitemap);
      }

    } catch (error) {
      console.error('SitemapGenerator: Error generating sitemap:', error);
    }
  }, [autoGenerate, services, projects, isLoading, error, onGenerated, verbose]);

  // This component doesn't render anything visible
  // It's a utility component for build-time sitemap generation
  return null;
};

// Export the hook in a separate file to avoid fast refresh warning
// This is a utility hook, not a component


export default SitemapGenerator;
