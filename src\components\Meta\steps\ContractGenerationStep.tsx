import { <PERSON>, Button } from '@/ui';
import { ContractStepProps } from '@/lib/meta/types';
import { formatContractDate } from '@/lib/meta/utils/form-helpers';
import { ArrowLeft, Download, FileText, CheckCircle, Eye } from 'lucide-react';
import { pdf } from '@react-pdf/renderer';
import ContractPDF from '../ContractPDFTemplate';

const ContractGenerationStep = ({ formData, onPrev }: ContractStepProps) => {

  const generatePDFBlob = async () => {
    const blob = await pdf(<ContractPDF formData={formData} />).toBlob();
    return blob;
  };

  const handleDownload = async () => {
    try {
      const blob = await generatePDFBlob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Arbeidskontrakt_${formData.employeeName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Det oppstod en feil ved generering av PDF. Vennligst prøv igjen.');
    }
  };

  const handlePreview = async () => {
    try {
      const blob = await generatePDFBlob();
      const url = URL.createObjectURL(blob);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error generating PDF preview:', error);
      alert('Det oppstod en feil ved forhåndsvisning av PDF. Vennligst prøv igjen.');
    }
  };



  return (
    <div className="space-y-6">
      {/* Summary */}
      <Card title="Sammendrag av kontraktinformasjon" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Kontrakten er klar for generering</h3>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Personal Information */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">👤 Personopplysninger</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Navn:</span> {formData.employeeName}</div>
                <div><span className="font-medium">Adresse:</span> {formData.employeeAddress}</div>
                <div><span className="font-medium">Fødselsdato:</span> {formatContractDate(formData.employeeBirthDate)}</div>
                <div><span className="font-medium">Kontonummer:</span> {formData.accountNumber}</div>
              </div>
            </div>

            {/* Position & Employment */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">💼 Stilling & Ansettelse</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Stilling:</span> {formData.position}</div>
                <div><span className="font-medium">Startdato:</span> {formatContractDate(formData.startDate)}</div>
                <div><span className="font-medium">Type:</span> {formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</div>
                {formData.isTemporary && formData.temporaryEndDate && (
                  <div><span className="font-medium">Sluttdato:</span> {formatContractDate(formData.temporaryEndDate)}</div>
                )}
                <div><span className="font-medium">Prøvetid:</span> {formData.probationPeriod ? `${formData.probationMonths} måneder` : 'Nei'}</div>
              </div>
            </div>

            {/* Work Conditions */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">⏰ Arbeidsvilkår</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Arbeidstid:</span> {formData.workingHoursPerWeek} t/uke</div>
                <div><span className="font-medium">Timelønn:</span> kr {formData.hourlyRate},-</div>
                <div><span className="font-medium">Overtidstillegg:</span> {formData.overtimeRate}%</div>
                <div><span className="font-medium">Ferie:</span> {formData.vacationDays}</div>
                <div><span className="font-medium">Eget verktøy:</span> {formData.ownTools ? 'Ja' : 'Nei'}</div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="mt-6 space-y-4">
            {/* Job Description */}
            {formData.positionDescription && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h5 className="font-medium text-gray-900 mb-2">📋 Arbeidsoppgaver</h5>
                <p className="text-sm text-gray-600">{formData.positionDescription}</p>
              </div>
            )}

            {/* Temporary Employment Reason */}
            {formData.isTemporary && formData.temporaryReason && (
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h5 className="font-medium text-gray-900 mb-2">⚠️ Begrunnelse for midlertidig ansettelse</h5>
                <p className="text-sm text-gray-600">{formData.temporaryReason}</p>
              </div>
            )}

            {/* Company Information */}
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h5 className="font-medium text-gray-900 mb-2">🏢 Bedriftsinformasjon</h5>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Bedrift:</span> {formData.companyName}</div>
                <div><span className="font-medium">Org.nr:</span> {formData.companyOrgNumber}</div>
                <div><span className="font-medium">Adresse:</span> {formData.companyAddress}</div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Actions */}
      <Card title="Generer PDF-kontrakt" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <FileText className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Last ned eller forhåndsvis PDF-kontrakten</h3>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
              <div className="text-sm">
                <p className="text-blue-800 font-medium">Kontrakten er klar for generering</p>
                <p className="text-blue-700 mt-1">
                  Kontrakten er basert på Arbeidsmiljøloven § 14-6. Vennligst gjennomgå informasjonen før signering.
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleDownload}
              variant="primary"
              size="lg"
              className="flex-1"
            >
              <Download className="h-5 w-5 mr-2" />
              Last ned PDF
            </Button>

            <Button
              onClick={handlePreview}
              variant="secondary"
              size="lg"
              className="flex-1"
            >
              <Eye className="h-5 w-5 mr-2" />
              Forhåndsvis PDF
            </Button>
          </div>
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-start">
        <Button
          onClick={onPrev}
          variant="secondary"
          size="lg"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Forrige steg
        </Button>
      </div>
    </div>
  );
};

export default ContractGenerationStep;
