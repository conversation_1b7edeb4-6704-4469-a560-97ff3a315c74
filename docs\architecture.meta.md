# Comprehensive Codebase Analysis: Ringerike Landskap

This document defines the streamlined, unified architecture of the Ringerike Landskap website after systematic consolidation and bloat removal. This is a mermaid diagram that illustrate how the codebase branches converge, with special focus on the Arbeidskontrakt Generator utility within the Meta Utilities system.

## Codebase Convergence Diagram

```mermaid
flowchart TD
    %% Core Application Structure
    root[Ringerike Landskap Website] --> app[App Entry<br/>src/app/index.tsx]
    app --> router[Routing Layer]

    %% Main Routing Branches
    router --> mainRoutes[Main Website Routes]
    router --> metaRoutes[Meta Routes<br/>src/components/Meta/MetaRouter.tsx]

    %% Main Website Structure
    mainRoutes --> pages[Page Components]
    pages --> homePage[HomePage<br/>src/pages/HomePage.tsx]

    %% Main Website Sections
    homePage --> sections[Section Components]
    sections --> home[10-home]
    sections --> about[20-about]
    sections --> services[30-services]
    sections --> projects[40-projects]
    sections --> testimonials[50-testimonials]
    sections --> contact[60-contact]

    %% Meta Utilities Structure
    metaRoutes --> metaPages[Meta Pages<br/>src/pages/meta/*]
    metaPages --> metaIndex[MetaIndexPage]
    metaPages --> arbeidskontraktPage[ArbeidskontraktPage]
    metaPages --> logoPage[LogoPage]

    %% Error Boundary Protection
    arbeidskontraktPage --> errorBoundary[MetaErrorBoundary<br/>src/components/Meta/MetaErrorBoundary.tsx]
    errorBoundary --> arbeidskontraktGen[ArbeidskontraktGenerator<br/>src/components/Meta/ArbeidskontraktGenerator.tsx]

    %% Contract Generator Workflow
    arbeidskontraktGen --> stepSystem[Multi-Step Wizard]
    stepSystem --> step1[BasicInfoStep<br/>src/components/Meta/steps/BasicInfoStep.tsx]
    stepSystem --> step2[AdvancedSettingsStep<br/>src/components/Meta/steps/AdvancedSettingsStep.tsx]
    stepSystem --> step3[ContractGenerationStep<br/>src/components/Meta/steps/ContractGenerationStep.tsx]
    step3 --> pdfTemplate[ContractPDFTemplate<br/>src/components/Meta/ContractPDFTemplate.tsx]

    %% Supporting Libraries - Meta Specific
    metaLib[Meta Library<br/>src/lib/meta/*] --> metaConstants[Company Constants<br/>src/lib/meta/constants/company.ts]
    metaLib --> metaTypes[Contract Types<br/>src/lib/meta/types/contract.ts]
    metaLib --> metaUtils[Form Helpers<br/>src/lib/meta/utils/form-helpers.ts]
    metaLib --> metaDesign[PDF Design System<br/>src/lib/meta/design/contract-design-system.ts]

    %% UI Components
    uiComponents[UI Components<br/>src/ui/*] --> button[Button]
    uiComponents --> card[Card]
    uiComponents --> container[Container]
    uiComponents --> formComponents[Form Components]

    %% Form Components Detail
    formComponents --> input[Input]
    formComponents --> select[Select]
    formComponents --> dateInput[DateInput]
    formComponents --> textarea[Textarea]

    %% Shared Libraries
    sharedLib[Shared Libraries<br/>src/lib/*] --> apiLayer[API Layer<br/>src/lib/api/*]
    sharedLib --> configLayer[Config Layer<br/>src/lib/config/*]
    sharedLib --> constantsLayer[Constants<br/>src/lib/constants/*]
    sharedLib --> contextLayer[Context<br/>src/lib/context/*]
    sharedLib --> hooksLayer[Hooks<br/>src/lib/hooks/*]
    sharedLib --> typesLayer[Types<br/>src/lib/types/*]
    sharedLib --> utilsLayer[Utils<br/>src/lib/utils/*]

    %% Key Relationships - Contract Generator
    arbeidskontraktGen --> metaConstants
    arbeidskontraktGen --> metaTypes
    arbeidskontraktGen --> metaUtils
    arbeidskontraktGen --> formComponents
    pdfTemplate --> metaDesign
    pdfTemplate --> metaConstants

    %% Key Relationships - Form Components
    step1 --> formComponents
    step2 --> formComponents
    step3 --> formComponents

    %% Layout Components
    layoutComponents[Layout<br/>src/layout/*] --> header[Header]
    layoutComponents --> footer[Footer]
    layoutComponents --> meta[Meta]

    %% Integration Points
    app --> layoutComponents

    %% Style nodes
    classDef meta fill:#e6f7ff,stroke:#333,stroke-width:1px;
    classDef mainSite fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef core fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef ui fill:#ffe6cc,stroke:#333,stroke-width:1px;
    classDef lib fill:#e1d5e7,stroke:#333,stroke-width:1px;

    class metaRoutes,metaPages,metaIndex,arbeidskontraktPage,logoPage,errorBoundary,arbeidskontraktGen,stepSystem,step1,step2,step3,pdfTemplate,metaLib,metaConstants,metaTypes,metaUtils,metaDesign meta;
    class mainRoutes,pages,homePage,sections,home,about,services,projects,testimonials,contact mainSite;
    class root,app,router core;
    class uiComponents,button,card,container,formComponents,input,select,dateInput,textarea,layoutComponents,header,footer,meta ui;
    class sharedLib,apiLayer,configLayer,constantsLayer,contextLayer,hooksLayer,typesLayer,utilsLayer lib;
```

## Key Architectural Insights

1. **Architectural Isolation Pattern**
   - The Meta Utilities system (including the Arbeidskontrakt Generator) is completely isolated from the main website
   - Error boundaries prevent Meta utility failures from affecting the main site
   - Separate routing system (`/meta/*`) keeps concerns separated

2. **Multi-Step Wizard Pattern**
   - The contract generator follows a progressive disclosure pattern with three distinct steps
   - Each step has its own component with focused responsibility
   - Form state is maintained throughout the wizard flow

3. **PDF Generation System**
   - Dedicated PDF template component (`ContractPDFTemplate.tsx`)
   - Custom design system for PDF styling (`contract-design-system.ts`)
   - Company constants provide standardized information

4. **Shared Component Architecture**
   - Form components from `src/ui/Form/` are reused across both main site and Meta utilities
   - UI components follow a consistent directory structure with index.tsx files

5. **Library Organization**
   - Meta-specific utilities are isolated in `src/lib/meta/`
   - Shared utilities in `src/lib/` support both main site and Meta utilities
   - Clear separation between general and Meta-specific types

