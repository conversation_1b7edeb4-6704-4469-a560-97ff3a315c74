/**
 * Sitemap Generation Utility
 * 
 * Generates XML sitemap following the filestructure-first principle.
 * Integrates with existing data sources and routing structure.
 */

import { SERVICES, PROJECTS } from '@/lib/constants/data';
import { SEO_CONFIG } from '@/lib/config/seo';
import type { SitemapUrl, Sitemap, SitemapGenerationOptions } from '@/lib/types/seo';

/**
 * Static pages from the main routing structure
 * Based on routes defined in src/app/index.tsx
 */
const STATIC_PAGES: SitemapUrl[] = [
  {
    loc: '/',
    changefreq: 'weekly',
    priority: SEO_CONFIG.sitemap.priority.home,
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    loc: '/hvem',
    changefreq: 'monthly',
    priority: SEO_CONFIG.sitemap.priority.main_pages
  },
  {
    loc: '/hva',
    changefreq: 'weekly',
    priority: SEO_CONFIG.sitemap.priority.main_pages
  },
  {
    loc: '/prosjekter',
    changefreq: 'weekly',
    priority: SEO_CONFIG.sitemap.priority.main_pages
  },
  {
    loc: '/kontakt',
    changefreq: 'monthly',
    priority: SEO_CONFIG.sitemap.priority.main_pages
  },
  {
    loc: '/kundehistorier',
    changefreq: 'monthly',
    priority: SEO_CONFIG.sitemap.priority.main_pages
  }
];

/**
 * Generate service detail page URLs
 * Uses existing SERVICES data from constants/data.ts
 */
const generateServiceUrls = (): SitemapUrl[] => {
  return SERVICES.map(service => ({
    loc: `/tjenester/${service.id}`,
    changefreq: 'monthly' as const,
    priority: SEO_CONFIG.sitemap.priority.service_pages,
    lastmod: new Date().toISOString().split('T')[0]
  }));
};

/**
 * Generate project detail page URLs
 * Uses existing PROJECTS data from constants/data.ts
 */
const generateProjectUrls = (): SitemapUrl[] => {
  return PROJECTS.map(project => ({
    loc: `/prosjekter/${project.id}`,
    changefreq: 'monthly' as const,
    priority: SEO_CONFIG.sitemap.priority.project_pages,
    lastmod: new Date().toISOString().split('T')[0]
  }));
};

/**
 * Generate location-specific page URLs
 * Uses existing SERVICE_AREAS data from constants/locations.ts
 * Follows established local SEO patterns: hole, ringerike, royse, honefoss
 */
const generateLocationUrls = (): SitemapUrl[] => {
  // Use the established location slugs from the existing SEO strategy
  const locationSlugs = ['hole', 'ringerike', 'royse', 'honefoss'];

  return locationSlugs.map(slug => ({
    loc: `/${slug}`,
    changefreq: 'monthly' as const,
    priority: slug === 'ringerike' ? 0.8 : SEO_CONFIG.sitemap.priority.detail_pages, // Ringerike is main area
    lastmod: new Date().toISOString().split('T')[0]
  }));
};

/**
 * Generate complete sitemap data
 * @param options - Generation options
 * @returns Complete sitemap object
 */
export const generateSitemapData = (options: Partial<SitemapGenerationOptions> = {}): Sitemap => {
  const {
    includeServices = true,
    includeProjects = true,
    includeLocationPages = true,
    excludePaths = [],
    lastModified = new Date()
  } = options;

  let urls: SitemapUrl[] = [...STATIC_PAGES];

  // Add service pages if enabled
  if (includeServices) {
    urls = urls.concat(generateServiceUrls());
  }

  // Add project pages if enabled
  if (includeProjects) {
    urls = urls.concat(generateProjectUrls());
  }

  // Add location pages if enabled
  if (includeLocationPages) {
    urls = urls.concat(generateLocationUrls());
  }

  // Filter out excluded paths
  if (excludePaths.length > 0) {
    urls = urls.filter(url => !excludePaths.some(excluded => url.loc.startsWith(excluded)));
  }

  // Ensure all URLs are absolute
  urls = urls.map(url => ({
    ...url,
    loc: url.loc.startsWith('http') ? url.loc : `${SEO_CONFIG.canonicalBaseUrl}${url.loc}`
  }));

  return {
    urls,
    lastGenerated: lastModified.toISOString()
  };
};

/**
 * Generate XML sitemap string
 * @param sitemapData - Sitemap data object
 * @returns XML sitemap string
 */
export const generateSitemapXML = (sitemapData: Sitemap): string => {
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
  const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
  const urlsetClose = '</urlset>';

  const urlEntries = sitemapData.urls.map(url => {
    let urlXML = `  <url>\n    <loc>${url.loc}</loc>`;
    
    if (url.lastmod) {
      urlXML += `\n    <lastmod>${url.lastmod}</lastmod>`;
    }
    
    if (url.changefreq) {
      urlXML += `\n    <changefreq>${url.changefreq}</changefreq>`;
    }
    
    if (url.priority !== undefined) {
      urlXML += `\n    <priority>${url.priority}</priority>`;
    }
    
    urlXML += '\n  </url>';
    return urlXML;
  }).join('\n');

  return [xmlHeader, urlsetOpen, urlEntries, urlsetClose].join('\n');
};

/**
 * Generate complete XML sitemap
 * @param options - Generation options
 * @returns XML sitemap string
 */
export const generateSitemap = (options: Partial<SitemapGenerationOptions> = {}): string => {
  const sitemapData = generateSitemapData(options);
  return generateSitemapXML(sitemapData);
};

/**
 * Get sitemap URLs for robots.txt
 * @returns Array of sitemap URLs
 */
export const getSitemapUrls = (): string[] => {
  return [`${SEO_CONFIG.canonicalBaseUrl}${SEO_CONFIG.sitemap.path}`];
};
