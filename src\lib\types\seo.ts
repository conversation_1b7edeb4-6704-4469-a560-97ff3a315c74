/**
 * SEO Type Definitions
 * 
 * TypeScript types for SEO-related functionality.
 * Follows the existing types organization pattern.
 */

/**
 * Sitemap URL entry
 */
export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

/**
 * Complete sitemap structure
 */
export interface Sitemap {
  urls: SitemapUrl[];
  lastGenerated: string;
}

/**
 * Hreflang link attributes
 */
export interface HreflangLink {
  rel: 'alternate';
  hreflang: string;
  href: string;
}

/**
 * Canonical link attributes
 */
export interface CanonicalLink {
  rel: 'canonical';
  href: string;
}

/**
 * Breadcrumb item for schema.org
 */
export interface BreadcrumbItem {
  '@type': 'ListItem';
  position: number;
  name: string;
  item?: string;
}

/**
 * Breadcrumb list schema
 */
export interface BreadcrumbListSchema {
  '@context': 'https://schema.org';
  '@type': 'BreadcrumbList';
  itemListElement: BreadcrumbItem[];
}

/**
 * FAQ schema item
 */
export interface FAQItem {
  '@type': 'Question';
  name: string;
  acceptedAnswer: {
    '@type': 'Answer';
    text: string;
  };
}

/**
 * FAQ page schema
 */
export interface FAQPageSchema {
  '@context': 'https://schema.org';
  '@type': 'FAQPage';
  mainEntity: FAQItem[];
}

/**
 * Enhanced SEO data interface extending the existing one
 */
export interface EnhancedSEOData {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl: string;
  hreflangLinks: HreflangLink[];
  schema: Record<string, unknown>;
  breadcrumbSchema?: BreadcrumbListSchema;
  faqSchema?: FAQPageSchema;
  openGraph: {
    title: string;
    description: string;
    image: string;
    url: string;
    type: 'website' | 'article';
    locale: string;
  };
  twitter: {
    card: 'summary_large_image';
    title: string;
    description: string;
    image: string;
  };
}

/**
 * Image SEO attributes
 */
export interface ImageSEO {
  src: string;
  alt: string;
  title?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
}

/**
 * Page type for SEO optimization
 */
export type PageType = 'home' | 'about' | 'services' | 'service-detail' | 'projects' | 'project-detail' | 'contact' | 'testimonials';

/**
 * SEO audit result
 */
export interface SEOAuditResult {
  page: string;
  issues: {
    type: 'error' | 'warning' | 'info';
    message: string;
    element?: string;
  }[];
  score: number;
  recommendations: string[];
}

/**
 * Sitemap generation options
 */
export interface SitemapGenerationOptions {
  includeServices: boolean;
  includeProjects: boolean;
  includeLocationPages: boolean;
  excludePaths: string[];
  lastModified?: Date;
}
