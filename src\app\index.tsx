import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import Header from "@/layout/Header";
import Footer from "@/layout/Footer";

// Import page components
import HomePage from "@/sections/10-home";
import AboutPage from "@/sections/20-about";
import ServicesPage from "@/sections/30-services";
import ServiceDetailPage from "@/sections/30-services/detail";
import ProjectsPage from "@/sections/40-projects";
import ProjectDetailPage from "@/sections/40-projects/detail";
import ContactPage from "@/sections/60-contact";
import TestimonialsPage from "@/sections/50-testimonials";

// Import isolated meta router
import MetaRouter from "@/components/Meta/MetaRouter";



function App() {
    return (
        <HelmetProvider>
            <Router>
                <div className="flex flex-col min-h-screen">
                    <Header />
                    <main className="flex-grow">
                        <Routes>
                            <Route path="/" element={<HomePage />} />
                            <Route path="/hvem" element={<AboutPage />} />
                            <Route path="/hva" element={<ServicesPage />} />
                            {/* Redirect /tjenester to /hva */}
                            <Route path="/tjenester" element={<Navigate to="/hva" replace />} />
                            <Route
                                path="/tjenester/:id"
                                element={<ServiceDetailPage />}
                            />
                            <Route
                                path="/prosjekter"
                                element={<ProjectsPage />}
                            />
                            <Route
                                path="/prosjekter/:id"
                                element={<ProjectDetailPage />}
                            />
                            <Route path="/kontakt" element={<ContactPage />} />
                            <Route
                                path="/kundehistorier"
                                element={<TestimonialsPage />}
                            />
                            {/* Meta utilities - isolated router with error boundaries */}
                            <Route path="/meta/*" element={<MetaRouter />} />
                            {/* Legacy redirect for old logo URL */}
                            <Route path="/logo" element={<Navigate to="/meta/logo" replace />} />

                        </Routes>
                    </main>
                    <Footer />
                </div>
            </Router>
        </HelmetProvider>
    );
}

export default App;
