/**
 * Enhanced API utilities with logging and error handling
 *
 * This module extends the base API layer with additional functionality:
 * - Logging for debugging and performance monitoring
 * - Consistent error handling
 * - Cache management for improved performance
 *
 * This is part of the API consolidation effort to create a single source of truth
 * for data access throughout the application.
 */

// Import from constants to avoid direct data imports
import {
  SERVICES,
  PROJECTS,
  TESTIMONIALS,
  SERVICE_AREAS
} from '@/lib/constants/data';
import { SERVICE_TO_IMAGE_CATEGORY } from '@/lib/config/images';
import {
  SEASONAL_SERVICES,
  SEASONAL_PROJECTS
} from '@/lib/constants/seasonal';
import { ProjectType, ServiceType, TestimonialType, ServiceArea } from '@/lib/types';
import { logDataAccess, logApiOperation, logSeasonalAccess } from '@/lib/utils/debug';
import {
  filterServices,
  filterProjects,
  getCurrentSeason
} from '@/lib/utils/filtering';

// Simple in-memory cache
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

const cache: Record<string, CacheEntry<any>> = {};
const DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Get data from cache or fetch it using the provided function
 * @param cacheKey The cache key
 * @param fetchFn The function to fetch the data if not in cache
 * @param ttl Time to live in milliseconds
 * @returns The cached or freshly fetched data
 */
async function getFromCacheOrFetch<T>(
  cacheKey: string,
  fetchFn: () => Promise<T>,
  ttl: number = DEFAULT_CACHE_TTL
): Promise<T> {
  const now = Date.now();
  const cacheEntry = cache[cacheKey];

  // Return from cache if valid
  if (cacheEntry && cacheEntry.expiresAt > now) {
    logDataAccess('direct', cacheKey, { hit: true, age: now - cacheEntry.timestamp });
    return cacheEntry.data;
  }

  // Fetch fresh data
  logDataAccess('api', cacheKey, { hit: false });
  const startTime = performance.now();

  try {
    const data = await fetchFn();

    // Store in cache
    cache[cacheKey] = {
      data,
      timestamp: now,
      expiresAt: now + ttl
    };

    logApiOperation(`fetch:${cacheKey}`, startTime, data);
    return data;
  } catch (error) {
    logApiOperation(`error:${cacheKey}`, startTime, error);
    throw error;
  }
}

/**
 * Clear the entire cache or a specific entry
 * @param cacheKey Optional specific cache key to clear
 */
export function clearCache(cacheKey?: string): void {
  if (cacheKey) {
    delete cache[cacheKey];
    logDataAccess('direct', 'clear', { key: cacheKey });
  } else {
    Object.keys(cache).forEach(key => delete cache[key]);
    logDataAccess('direct', 'clear', { all: true });
  }
}

// Re-export seasonal constants for backward compatibility
// These are now imported from lib/constants/seasonal
export { SEASONAL_SERVICES, SEASONAL_PROJECTS };

/**
 * Get all services
 */
export const getServices = async (): Promise<ServiceType[]> => {
  return getFromCacheOrFetch('services', async () => {
    logDataAccess('direct', 'services');
    return SERVICES;
  });
};

/**
 * Get a service by ID
 */
export const getServiceById = async (id: string): Promise<ServiceType | undefined> => {
  return getFromCacheOrFetch(`service:${id}`, async () => {
    logDataAccess('direct', 'service', { id });
    return SERVICES.find(service => service.id === id);
  });
};

/**
 * Get service image category
 */
export const getServiceImageCategory = async (serviceId: string): Promise<string | undefined> => {
  return getFromCacheOrFetch(`serviceImageCategory:${serviceId}`, async () => {
    logDataAccess('direct', 'serviceImageCategory', { serviceId });
    // First try to get from the service object
    const service = SERVICES.find(service => service.id === serviceId);
    if (service?.imageCategory) return service.imageCategory;

    // If not found in the service, try the mapping
    return SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
  });
};

/**
 * Get services filtered by criteria
 */
export const getFilteredServices = async (
  category?: string,
  feature?: string,
  season?: string
): Promise<ServiceType[]> => {
  const cacheKey = `filteredServices:${category || ''}:${feature || ''}:${season || ''}`;

  return getFromCacheOrFetch(cacheKey, async () => {
    logDataAccess('direct', 'filteredServices', { category, feature, season });

    // Use the centralized filtering utility
    return filterServices(SERVICES, category || null, feature || null, season || null);
  });
};

/**
 * Get seasonal services based on current season
 */
export const getSeasonalServices = async (limit?: number): Promise<ServiceType[]> => {
  const currentSeason = getCurrentSeason();
  const cacheKey = `seasonalServices:${currentSeason}:${limit || 'all'}`;

  return getFromCacheOrFetch(cacheKey, async () => {
    logDataAccess('direct', 'seasonalServices', { season: currentSeason, limit });
    logSeasonalAccess('API', currentSeason, { method: 'getSeasonalServices', limit });

    // Use the centralized filtering utility via getFilteredServices
    const filtered = await getFilteredServices(undefined, undefined, currentSeason);
    return limit ? filtered.slice(0, limit) : filtered;
  });
};

/**
 * Get all projects
 */
export const getProjects = async (): Promise<ProjectType[]> => {
  return getFromCacheOrFetch('projects', async () => {
    logDataAccess('direct', 'projects');
    return PROJECTS;
  });
};

/**
 * Get a project by ID
 */
export const getProjectById = async (id: string): Promise<ProjectType | undefined> => {
  return getFromCacheOrFetch(`project:${id}`, async () => {
    logDataAccess('direct', 'project', { id });
    return PROJECTS.find(project => project.id === id);
  });
};

/**
 * Get projects filtered by criteria
 */
export const getFilteredProjects = async (
  category?: string,
  location?: string,
  tag?: string,
  season?: string
): Promise<ProjectType[]> => {
  const cacheKey = `filteredProjects:${category || ''}:${location || ''}:${tag || ''}:${season || ''}`;

  return getFromCacheOrFetch(cacheKey, async () => {
    logDataAccess('direct', 'filteredProjects', { category, location, tag, season });

    // Use the centralized filtering utility
    return filterProjects(PROJECTS, category || null, location || null, tag || null, season || null);
  });
};

/**
 * Get seasonal projects based on current season
 */
export const getSeasonalProjects = async (limit?: number): Promise<ProjectType[]> => {
  const currentSeason = getCurrentSeason();
  const cacheKey = `seasonalProjects:${currentSeason}:${limit || 'all'}`;

  return getFromCacheOrFetch(cacheKey, async () => {
    logDataAccess('direct', 'seasonalProjects', { season: currentSeason, limit });
    logSeasonalAccess('API', currentSeason, { method: 'getSeasonalProjects', limit });

    // Use the centralized filtering utility via getFilteredProjects
    const filtered = await getFilteredProjects(undefined, undefined, undefined, currentSeason);

    // If we don't have enough seasonal projects, add some recent ones
    if (limit && filtered.length < limit) {
      const additionalProjects = PROJECTS
        .filter(p => !filtered.some(f => f.id === p.id))
        .slice(0, limit - filtered.length);

      return [...filtered, ...additionalProjects];
    }

    return limit ? filtered.slice(0, limit) : filtered;
  });
};

/**
 * Get unique project categories
 */
export const getProjectCategories = async (): Promise<string[]> => {
  return getFromCacheOrFetch('projectCategories', async () => {
    logDataAccess('direct', 'projectCategories');
    return [...new Set(PROJECTS.map(project => project.category))];
  });
};

/**
 * Get unique project locations
 */
export const getProjectLocations = async (): Promise<string[]> => {
  return getFromCacheOrFetch('projectLocations', async () => {
    logDataAccess('direct', 'projectLocations');
    return [...new Set(PROJECTS.map(project => project.location))];
  });
};

/**
 * Get unique project tags
 */
export const getProjectTags = async (): Promise<string[]> => {
  return getFromCacheOrFetch('projectTags', async () => {
    logDataAccess('direct', 'projectTags');
    const allTags = PROJECTS.flatMap(project => project.tags);
    return [...new Set(allTags)];
  });
};

/**
 * Get all testimonials
 */
export const getTestimonials = async (): Promise<TestimonialType[]> => {
  return getFromCacheOrFetch('testimonials', async () => {
    logDataAccess('direct', 'testimonials');
    return TESTIMONIALS;
  });
};

/**
 * Get testimonials filtered by rating
 */
export const getFilteredTestimonials = async (rating?: number): Promise<TestimonialType[]> => {
  const cacheKey = `filteredTestimonials:${rating || 'all'}`;

  return getFromCacheOrFetch(cacheKey, async () => {
    logDataAccess('direct', 'filteredTestimonials', { rating });
    if (rating) {
      return TESTIMONIALS.filter(testimonial => testimonial.rating === rating);
    }
    return TESTIMONIALS;
  });
};

/**
 * Get testimonial rating counts
 */
export const getTestimonialRatingCounts = async (): Promise<Record<number, number>> => {
  return getFromCacheOrFetch('testimonialRatingCounts', async () => {
    logDataAccess('direct', 'testimonialRatingCounts');
    const counts: Record<number, number> = {};
    TESTIMONIALS.forEach(testimonial => {
      counts[testimonial.rating] = (counts[testimonial.rating] || 0) + 1;
    });
    return counts;
  });
};

/**
 * Get service areas
 */
export const getServiceAreas = async (): Promise<ServiceArea[]> => {
  return getFromCacheOrFetch('serviceAreas', async () => {
    logDataAccess('direct', 'serviceAreas');
    return SERVICE_AREAS;
  });
};
