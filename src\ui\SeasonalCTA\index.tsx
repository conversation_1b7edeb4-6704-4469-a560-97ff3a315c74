import React from "react";
import { Link } from "react-router-dom";
import { useSeasonalData } from '@/lib/hooks';
import { logSeasonalAccess } from '@/lib/utils/debug';
import { PRIMARY_AREA } from '@/lib/constants/locations';

interface SeasonalCTAProps {
    season?: "spring" | "summer" | "fall" | "winter";
    className?: string;
}

const SeasonalCTA: React.FC<SeasonalCTAProps> = ({
    season,
    className = "",
}) => {
    // Use our new seasonal hook to get seasonal data
    const {
        currentSeason,
        currentSeasonEnglish
    } = useSeasonalData();

    // Log seasonal access for debugging
    logSeasonalAccess('SeasonalCTA', currentSeason, {
        providedSeason: season
    });

    // If a specific season is provided via prop, use that; otherwise use the detected season
    const activeSeasonKey = season || currentSeasonEnglish;

    const seasonData = {
        spring: {
            title: `Planlegg for ${PRIMARY_AREA.name}-v<PERSON><PERSON>`,
            description:
                `Få personlig veiledning basert på lang erfaring med ${PRIMARY_AREA.name}s unike forhold.`,
            icon: "🌱",
            services: [
                "Ferdigplen og beplantning",
                "Belegningsstein og støttemur",
                "Sesongtilpasset rådgivning",
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
        summer: {
            title: "Nyt sommeren i en vakker hage",
            description:
                "Få mest mulig ut av sommeren med en velstelt og innbydende hage.",
            icon: "☀️",
            services: [
                "Rask etablering av ferdigplen",
                "Støttemurer og kantstein",
                "Profesjonell beplantning",
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
        fall: {
            title: "Høstklargjøring og planlegging",
            description:
                "Forbered hagen for vinteren og planlegg neste års prosjekter.",
            icon: "🍂",
            services: [
                "Høstbeplantning og beskjæring",
                "Vinterklargjøring",
                "Oppgradering av uterom",
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
        winter: {
            title: `Planlegg for ${PRIMARY_AREA.name}-våren`,
            description:
                `Perfekt tid for å planlegge neste sesongs hageprosjekter.`,
            icon: "❄️",
            services: [
                "Planlegging for våren",
                "Design og tegningstjenester",
                "Vintervedlikehold",
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
    };

    // Get the content for the active season
    const { title, description, icon, services, cta } =
        seasonData[activeSeasonKey];

    return (
        <div
            className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}
        >
            <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-blue-600">
                    <span>{icon}</span>
                    <span>Sesong: {currentSeason}</span>
                </div>

                <h3 className="text-xl font-semibold">{title}</h3>

                <p className="text-gray-600 text-sm">{description}</p>

                <div className="space-y-2 py-2">
                    <p className="text-gray-700 font-medium text-sm">
                        Aktuelle tjenester:
                    </p>
                    <ul className="space-y-1">
                        {services.map((service, index) => (
                            <li
                                key={index}
                                className="flex items-center gap-2 text-sm"
                            >
                                <span className="text-green-500">•</span>
                                <span>{service}</span>
                            </li>
                        ))}
                    </ul>
                </div>

                <Link
                    to="/kontakt"
                    className="block w-full bg-green-500 text-white text-center py-3 rounded-md hover:bg-green-600 transition-colors"
                >
                    {cta}
                </Link>
            </div>
        </div>
    );
};

export { SeasonalCTA };
export default SeasonalCTA;
