import React from "react";
import { Link } from "react-router-dom";
import { MapPin, TrendingUp } from "lucide-react";
import { ServiceArea  } from '@/lib/types';
// import { PROJECT_CATEGORY_TO_SERVICE_ID } from '@/lib/constants/data'; // Temporarily disabled

interface ServiceAreaListProps {
    areas: ServiceArea[];
    className?: string;
}

const ServiceAreaList: React.FC<ServiceAreaListProps> = ({
    areas,
    className = "",
}) => {
    return (
        <div
            className={`bg-white rounded-lg shadow-sm p-3 sm:p-4 ${className}`}
            itemScope
            itemType="http://schema.org/LocalBusiness"
            role="region"
            aria-label="Service area information"
        >
            <h3 className="text-xl font-semibold mb-1 sm:mb-2">Våre serviceområder</h3>
            <p className="text-gray-600 mb-2 sm:mb-3 text-sm">
                Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i følgende områder:
            </p>
            <div className="flex items-center gap-2 mb-3 p-2 bg-blue-50 rounded-md">
                <TrendingUp className="w-4 h-4 text-blue-600 flex-shrink-0" />
                <p className="text-xs text-blue-700">
                    Tjenestene under hver lokasjon viser våre mest populære arbeidsområder der, men vi utfører alle typer anleggsarbeid i hele regionen.
                </p>
            </div>

            <div className="space-y-2 sm:space-y-3">
                {areas.map((area, index) => (
                    <div
                        key={index}
                        className={`flex items-start p-2 sm:p-3 rounded-md ${
                            area.isBase
                                ? "bg-green-50 border-l-4 border-green-500"
                                : "hover:bg-gray-50"
                        }`}
                    >
                        <MapPin className="w-4 h-4 text-green-500 mt-1 mr-2 flex-shrink-0" />
                        <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-semibold text-sm">{area.city}</h4>
                                {area.distance && area.distance !== 'Hovedområde' && (
                                    <span className="text-xs text-gray-500">
                                        {area.distance}
                                    </span>
                                )}
                            </div>
                            {area.description && (
                                <div className="flex items-start gap-2">
                                    {/*<TrendingUp className="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" />*/}
                                    <div className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                                        {area.description.split(', ').map((service, index, array) => {
                                            // Get the service ID for URL (commented out for now)
                                            // const serviceId = PROJECT_CATEGORY_TO_SERVICE_ID[service as keyof typeof PROJECT_CATEGORY_TO_SERVICE_ID];

                                            return (
                                                <span key={service}>
                                                    {/* Temporarily disabled links until more projects are added */}
                                                    {/* <Link
                                                        to={`/prosjekter?category=${serviceId}&location=${encodeURIComponent(area.city)}`}
                                                        className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                                                    > */}
                                                        {service}
                                                    {/* </Link> */}
                                                    {index < array.length - 1 && ', '}
                                                </span>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-3 sm:mt-4 mb-1 sm:mb-2 text-center">
                <p className="text-gray-600 text-xs sm:text-sm mb-1.5 sm:mb-2">
                    Ta kontakt for en uforpliktende prat i Røyse, Hole, Vik, Sundvollen, Hønefoss, Jevnaker og Bærum.
                </p>
                <Link
                    to="/kontakt"
                    className="text-green-600 hover:text-green-700 font-medium text-xs sm:text-sm inline-block"
                >
                    Kontakt oss for tjenester i ditt område
                </Link>
            </div>
        </div>
    );
};

export { ServiceAreaList };
export default ServiceAreaList;
