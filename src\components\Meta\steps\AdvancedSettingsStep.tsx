import { <PERSON>, Button } from '@/ui';
import { Input, Select, Textarea, MaskedInput } from '@/ui/Form';
import { ContractStepProps, paymentDayOptions } from '@/lib/meta/types';
import { createFormFieldUpdater } from '@/lib/meta/utils/form-helpers';
import { ArrowLeft, ArrowRight, Building, Clock, Shield, Calendar } from 'lucide-react';

const AdvancedSettingsStep = ({ formData, updateFormData, onNext, onPrev }: ContractStepProps) => {
  // Use centralized form field updater
  const updateField = createFormFieldUpdater(updateFormData);

  return (
    <div className="space-y-6">
      {/* Section 2: Arbeidssted og arbeidsoppgaver */}
      <Card title="2. ARBEIDSSTED OG ARBEIDSOPPGAVER" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Building className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Bedriftsinformasjon og arbeidsoppgaver</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Bedriftsnavn"
              value={formData.companyName}
              onChange={(e) => updateField('companyName', e.target.value)}
            />

            <MaskedInput
              label="Organisasjonsnummer"
              value={formData.companyOrgNumber}
              onChange={(e) => updateField('companyOrgNumber', e.target.value)}
              maskType="organizationNumber"
            />
          </div>

          <Input
            label="Bedriftsadresse"
            value={formData.companyAddress}
            onChange={(e) => updateField('companyAddress', e.target.value)}
          />
        </div>
      </Card>

      {/* Section 4: Arbeidstid og lønn */}
      <Card title="4. ARBEIDSTID OG LØNN" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Clock className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Arbeidstid, lønn og godtgjørelser</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <MaskedInput
              label="Arbeidstid (timer per uke)"
              value={formData.workingHoursPerWeek}
              onChange={(e) => updateField('workingHoursPerWeek', Number(e.target.value))}
              maskType="hoursPerWeek"
            />

            <Input
              label="Arbeidstid (tidspunkt)"
              value={formData.workingTime}
              onChange={(e) => updateField('workingTime', e.target.value)}
              placeholder="07:00-15:00"
            />
          </div>

          <Textarea
            label="Pauser"
            value={formData.breakTime}
            onChange={(e) => updateField('breakTime', e.target.value)}
            rows={2}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Overtidstillegg (%)"
              type="number"
              value={formData.overtimeRate}
              onChange={(e) => updateField('overtimeRate', Number(e.target.value))}
            />

            <Select
              label="Utbetalingsdag"
              options={paymentDayOptions}
              value={formData.paymentDay.toString()}
              onChange={(e) => updateField('paymentDay', Number(e.target.value))}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Textarea
              label="Kjøregodtgjørelse"
              value={formData.travelAllowance}
              onChange={(e) => updateField('travelAllowance', e.target.value)}
              rows={2}
            />

            <Textarea
              label="Verktøygodtgjørelse"
              value={formData.toolAllowance}
              onChange={(e) => updateField('toolAllowance', e.target.value)}
              rows={2}
            />
          </div>
        </div>
      </Card>

      {/* Section 5: Ferie og permisjon */}
      <Card title="5. FERIE OG PERMISJON" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Calendar className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Ferie, sykepenger og permisjoner</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Textarea
              label="Ferie"
              value={formData.vacationDays}
              onChange={(e) => updateField('vacationDays', e.target.value)}
              rows={2}
            />

            <Textarea
              label="Feriepenger"
              value={formData.vacationPay}
              onChange={(e) => updateField('vacationPay', e.target.value)}
              rows={2}
            />
          </div>

          <Textarea
            label="Sykepenger"
            value={formData.sickPay}
            onChange={(e) => updateField('sickPay', e.target.value)}
            rows={2}
          />

          <Textarea
            label="Øvrig permisjon"
            value={formData.additionalLeaveRights}
            onChange={(e) => updateField('additionalLeaveRights', e.target.value)}
            rows={4}
          />
        </div>
      </Card>

      {/* Section 6: Oppsigelse og endringer */}
      <Card title="6. OPPSIGELSE OG ENDRINGER" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Shield className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Oppsigelse og varslingsregler</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Textarea
              label="Oppsigelsesfrister"
              value={formData.noticePeriod}
              onChange={(e) => updateField('noticePeriod', e.target.value)}
              rows={3}
            />

            <Textarea
              label="Kontraktvarighet (hvis relevant)"
              value={formData.contractDuration}
              onChange={(e) => updateField('contractDuration', e.target.value)}
              rows={3}
              placeholder="Kun relevant for midlertidige ansettelser - la stå tom for fast ansettelse"
            />
          </div>

          <Textarea
            label="Varslingsregler for endringer"
            value={formData.notificationRules}
            onChange={(e) => updateField('notificationRules', e.target.value)}
            rows={3}
          />
        </div>
      </Card>

      {/* Section 7: Pensjon og forsikring */}
      <Card title="7. PENSJON OG FORSIKRING" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Shield className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Pensjon og forsikringsordninger</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Pensjonsleverandør"
              value={formData.pensionProvider}
              onChange={(e) => updateField('pensionProvider', e.target.value)}
            />
            
            <MaskedInput
              label="Pensjon org.nr"
              value={formData.pensionOrgNumber}
              onChange={(e) => updateField('pensionOrgNumber', e.target.value)}
              maskType="organizationNumber"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Forsikringsleverandør"
              value={formData.insuranceProvider}
              onChange={(e) => updateField('insuranceProvider', e.target.value)}
            />
            
            <MaskedInput
              label="Forsikring org.nr"
              value={formData.insuranceOrgNumber}
              onChange={(e) => updateField('insuranceOrgNumber', e.target.value)}
              maskType="organizationNumber"
            />
          </div>
        </div>
      </Card>

      {/* Section 8: Øvrige bestemmelser */}
      <Card title="8. ØVRIGE BESTEMMELSER" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Shield className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Tariffavtale, kompetanse og HMS</h3>
          </div>

          <Textarea
            label="Tariffavtale"
            value={formData.tariffAgreement}
            onChange={(e) => updateField('tariffAgreement', e.target.value)}
            rows={2}
            placeholder="Oppgi hvilken tariffavtale som gjelder, eller 'Ingen tariffavtale'"
          />

          <Textarea
            label="Kompetanseutvikling"
            value={formData.competenceDevelopment}
            onChange={(e) => updateField('competenceDevelopment', e.target.value)}
            rows={3}
            placeholder="Beskriv bedriftens tilbud om opplæring, kurs og kompetanseutvikling"
          />

          <Textarea
            label="HMS-krav"
            value={formData.hmsRequirements}
            onChange={(e) => updateField('hmsRequirements', e.target.value)}
            rows={3}
            placeholder="Helse, miljø og sikkerhetskrav for stillingen"
          />
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          onClick={onPrev}
          variant="secondary"
          size="lg"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Forrige steg
        </Button>
        
        <Button
          onClick={onNext}
          variant="primary"
          size="lg"
        >
          Generer kontrakt
          <ArrowRight className="h-5 w-5 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default AdvancedSettingsStep;
