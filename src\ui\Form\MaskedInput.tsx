import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface MaskPattern {
  pattern: string; // e.g., "XXXX.XX.XXXXX" or "XXX XXX XXX"
  placeholder: string;
  example: string;
}

const MASK_PATTERNS: Record<string, MaskPattern> = {
  accountNumber: {
    pattern: 'XXXX.XX.XXXXX',
    placeholder: 'XXXX.XX.XXXXX',
    example: '1234.56.78901'
  },
  organizationNumber: {
    pattern: 'XXX XXX XXX',
    placeholder: 'XXX XXX XXX',
    example: '924 826 541'
  },
  hoursPerWeek: {
    pattern: 'XX.X',
    placeholder: 'XX.X',
    example: '37.5'
  }
};

interface MaskedInputProps {
  label?: string;
  error?: string;
  helper?: string;
  value?: string | number;
  onChange?: (e: { target: { value: string } }) => void;
  maskType: keyof typeof MASK_PATTERNS;
  required?: boolean;
  name?: string;
  id?: string;
  className?: string;
}

const MaskedInput = React.forwardRef<HTMLInputElement, MaskedInputProps>(
  ({ label, error, helper, value, onChange, maskType, required, name, id, className, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState('');
    const inputId = id || name;
    const mask = MASK_PATTERNS[maskType];

    // Apply mask formatting based on type
    const applyMask = (input: string, pattern: string): string => {
      // Remove all non-digit characters
      const digitsOnly = input.replace(/\D/g, '');
      
      let formatted = '';
      let digitIndex = 0;
      
      for (let i = 0; i < pattern.length && digitIndex < digitsOnly.length; i++) {
        const patternChar = pattern[i];
        
        if (patternChar === 'X') {
          // Insert digit
          formatted += digitsOnly[digitIndex];
          digitIndex++;
        } else {
          // Insert separator (dot, space, etc.)
          formatted += patternChar;
        }
      }
      
      return formatted;
    };

    // Remove mask formatting to get clean value
    const removeMask = (maskedValue: string): string => {
      return maskedValue.replace(/\D/g, '');
    };

    // Format value for display
    const formatForDisplay = (val: string | number): string => {
      if (!val) return '';
      const stringVal = val.toString();
      return applyMask(stringVal, mask.pattern);
    };

    // Update display value when prop value changes
    useEffect(() => {
      setDisplayValue(formatForDisplay(value || ''));
    }, [value, maskType]);

    // Handle input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const formatted = applyMask(inputValue, mask.pattern);
      
      setDisplayValue(formatted);
      
      // For hours per week, we want to preserve decimal
      if (maskType === 'hoursPerWeek') {
        // Allow decimal input for hours
        const cleanValue = inputValue.replace(/[^\d.]/g, '');
        if (onChange) {
          onChange({ target: { value: cleanValue } });
        }
      } else {
        // For other types, send clean digits only
        const cleanValue = removeMask(formatted);
        if (onChange) {
          onChange({ target: { value: cleanValue } });
        }
      }
    };

    // Handle blur to ensure final formatting
    const handleBlur = () => {
      if (displayValue) {
        const reformatted = formatForDisplay(displayValue);
        setDisplayValue(reformatted);
      }
    };

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <input
          ref={ref}
          id={inputId}
          name={name}
          type="text"
          value={displayValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={mask.placeholder}
          className={cn(
            'w-full rounded-md shadow-sm transition-colors duration-200',
            'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
            'px-3 py-2 text-sm',
            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            className
          )}
          {...props}
        />

        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
        
        <p className="mt-1 text-xs text-gray-400">
          Format: {mask.placeholder} (f.eks. {mask.example})
        </p>
      </div>
    );
  }
);

MaskedInput.displayName = 'MaskedInput';

export { MaskedInput };
export default MaskedInput;
