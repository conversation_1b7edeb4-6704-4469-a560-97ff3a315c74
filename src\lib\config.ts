/**
 * Application configuration
 * This file provides access to environment variables and configuration settings
 */

// Environment
export const ENV = {
  // Current environment
  environment: import.meta.env.MODE || 'development',
  
  // Is production environment
  isProduction: import.meta.env.PROD,
  
  // Is development environment
  isDevelopment: import.meta.env.DEV,
  
  // Base URL
  baseUrl: import.meta.env.VITE_BASE_URL || window.location.origin,
  
  // API URL
  apiUrl: import.meta.env.VITE_API_URL || `${window.location.origin}/api`,
  
  // Debug mode
  debug: import.meta.env.VITE_DEBUG === 'true',
  
  // Analytics
  analytics: {
    enabled: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
    id: import.meta.env.VITE_ANALYTICS_ID || '',
  },
  
  // Feature flags
  features: {
    contactForm: import.meta.env.VITE_FEATURE_CONTACT_FORM === 'true',
    testimonials: import.meta.env.VITE_FEATURE_TESTIMONIALS === 'true',
    projectsFilter: import.meta.env.VITE_FEATURE_PROJECTS_FILTER === 'true',
  },

  // SEO configuration
  seo: {
    enabled: import.meta.env.VITE_SEO_ENABLED !== 'false', // Default true
    generateSitemap: import.meta.env.VITE_GENERATE_SITEMAP !== 'false', // Default true
    hreflang: import.meta.env.VITE_HREFLANG || 'nb-NO',
  },
  
  // Build information
  build: {
    version: __APP_VERSION__,
    date: __BUILD_DATE__,
    env: __APP_ENV__,
  }
};

/**
 * Get a configuration value
 * @param path - Dot-notation path to the configuration value
 * @param defaultValue - Default value if the configuration value is not found
 * @returns The configuration value or the default value
 */
export function getConfig<T>(path: string, defaultValue?: T): T {
  const parts = path.split('.');
  let current: any = ENV;
  
  for (const part of parts) {
    if (current === undefined || current === null) {
      return defaultValue as T;
    }
    
    current = current[part];
  }
  
  return current !== undefined ? current : defaultValue as T;
}

/**
 * Check if a feature is enabled
 * @param feature - Feature name
 * @returns True if the feature is enabled
 */
export function isFeatureEnabled(feature: string): boolean {
  return getConfig<boolean>(`features.${feature}`, false);
}

/**
 * Log a debug message
 * @param message - Message to log
 * @param data - Additional data to log
 */
export function debug(message: string, ...data: any[]): void {
  if (ENV.debug) {
    console.log(`[DEBUG] ${message}`, ...data);
  }
}

export default ENV;
