import { SITE_CONFIG } from "../constants/site";

interface SEOProps {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article";
    path?: string;
}

export const generateSEOMeta = ({
    title,
    description = SITE_CONFIG.description,
    image = SITE_CONFIG.ogImage,
    type = "website",
    path = "",
}: SEOProps) => {
    const fullTitle = title
        ? `${title} | ${SITE_CONFIG.name}`
        : SITE_CONFIG.name;
    const url = `${SITE_CONFIG.url}${path}`;

    return {
        title: fullTitle,
        meta: [
            {
                name: "description",
                content: description,
            },
            {
                property: "og:title",
                content: fullTitle,
            },
            {
                property: "og:description",
                content: description,
            },
            {
                property: "og:image",
                content: image,
            },
            {
                property: "og:url",
                content: url,
            },
            {
                property: "og:type",
                content: type,
            },
            {
                name: "twitter:card",
                content: "summary_large_image",
            },
            {
                name: "twitter:title",
                content: fullTitle,
            },
            {
                name: "twitter:description",
                content: description,
            },
            {
                name: "twitter:image",
                content: image,
            },
        ],
        link: [
            {
                rel: "canonical",
                href: url,
            },
        ],
    };
};


