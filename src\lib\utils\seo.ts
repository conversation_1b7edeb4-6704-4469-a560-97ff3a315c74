import { SITE_CONFIG } from "../constants/site";
import type { BreadcrumbListSchema, BreadcrumbItem } from "@/lib/types/seo";

interface SEOProps {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article";
    path?: string;
}

export const generateSEOMeta = ({
    title,
    description = SITE_CONFIG.description,
    image = SITE_CONFIG.ogImage,
    type = "website",
    path = "",
}: SEOProps) => {
    const fullTitle = title
        ? `${title} | ${SITE_CONFIG.name}`
        : SITE_CONFIG.name;
    const url = `${SITE_CONFIG.url}${path}`;

    return {
        title: fullTitle,
        meta: [
            {
                name: "description",
                content: description,
            },
            {
                property: "og:title",
                content: fullTitle,
            },
            {
                property: "og:description",
                content: description,
            },
            {
                property: "og:image",
                content: image,
            },
            {
                property: "og:url",
                content: url,
            },
            {
                property: "og:type",
                content: type,
            },
            {
                name: "twitter:card",
                content: "summary_large_image",
            },
            {
                name: "twitter:title",
                content: fullTitle,
            },
            {
                name: "twitter:description",
                content: description,
            },
            {
                name: "twitter:image",
                content: image,
            },
        ],
        link: [
            {
                rel: "canonical",
                href: url,
            },
        ],
    };
};

/**
 * Generate breadcrumb schema for a given path
 * @param path - Current page path
 * @returns Breadcrumb list schema
 */
export const generateBreadcrumbSchema = (path: string): BreadcrumbListSchema => {
    const pathSegments = path.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always include home
    breadcrumbs.push({
        '@type': 'ListItem',
        position: 1,
        name: 'Hjem',
        item: SITE_CONFIG.url
    });

    // Map path segments to breadcrumb items
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
        currentPath += `/${segment}`;
        const position = index + 2;

        // Map segments to Norwegian names following existing SEO strategy
        const segmentNames: Record<string, string> = {
            'hvem': 'Om oss',
            'hva': 'Tjenester',
            'tjenester': 'Tjenester',
            'prosjekter': 'Prosjekter',
            'kontakt': 'Kontakt',
            'kundehistorier': 'Kundehistorier',
            // Local area pages following established SEO patterns
            'hole': 'Hole kommune',
            'ringerike': 'Ringerike',
            'royse': 'Røyse',
            'honefoss': 'Hønefoss'
        };

        const name = segmentNames[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
        const isLast = index === pathSegments.length - 1;

        breadcrumbs.push({
            '@type': 'ListItem',
            position,
            name,
            ...(isLast ? {} : { item: `${SITE_CONFIG.url}${currentPath}` })
        });
    });

    return {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: breadcrumbs
    };
};

/**
 * Generate enhanced SEO meta with breadcrumbs
 * @param props - SEO properties
 * @returns Enhanced SEO meta with breadcrumbs
 */
export const generateEnhancedSEOMeta = (props: SEOProps & { includeBreadcrumbs?: boolean }) => {
    const basicMeta = generateSEOMeta(props);

    if (props.includeBreadcrumbs && props.path) {
        const breadcrumbSchema = generateBreadcrumbSchema(props.path);
        return {
            ...basicMeta,
            breadcrumbSchema
        };
    }

    return basicMeta;
};

/**
 * Validate image alt tags on the page
 * @returns Array of images missing alt tags
 */
export const validateImageAltTags = (): { src: string; element: HTMLImageElement }[] => {
    if (typeof window === 'undefined') return [];

    const images = Array.from(document.querySelectorAll('img'));
    const missingAlt: { src: string; element: HTMLImageElement }[] = [];

    images.forEach(img => {
        if (!img.alt || img.alt.trim() === '') {
            missingAlt.push({
                src: img.src,
                element: img
            });
        }
    });

    return missingAlt;
};

/**
 * Generate SEO-friendly alt text for images
 * @param imagePath - Path to the image
 * @param context - Context where the image is used
 * @returns Generated alt text
 */
export const generateImageAltText = (imagePath: string, context?: string): string => {
    // Extract filename without extension
    const filename = imagePath.split('/').pop()?.split('.')[0] || '';

    // Convert kebab-case and snake_case to readable text
    const readable = filename
        .replace(/[-_]/g, ' ')
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .toLowerCase();

    // Capitalize first letter
    const capitalized = readable.charAt(0).toUpperCase() + readable.slice(1);

    // Add context if provided
    if (context) {
        return `${capitalized} - ${context}`;
    }

    return capitalized;
};


