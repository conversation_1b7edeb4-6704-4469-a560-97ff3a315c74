# Understanding the Arbeidskontrakt Generator PDF Design System

The Arbeidskontrakt (employment contract) generator uses a sophisticated design system for creating professional, legally-compliant PDF contracts. Here's how it works and how you can modify it:

## Design System Overview

The entire PDF styling is centralized in a single file:
```
src/lib/meta/design/contract-design-system.ts
```

This file contains a semantic design system organized into sections that directly match the contract structure:

1. **Foundation Tokens** - Core design variables (colors, typography, spacing)
2. **Semantic Design Sections** - Styling for each contract section
3. **Complete Design System Export** - The unified system

## How to Make Modifications

To modify the PDF design:

1. Open `src/lib/meta/design/contract-design-system.ts`
2. Locate the section you want to modify
3. Change the values as needed
4. Rebuild the project to see your changes

## Key Design Sections You Can Modify

### 1. Foundation Tokens
```typescript
export const foundationTokens = {
  // Brand Identity
  brand: {
    primary: '#000000',      // Main brand color (black)
    secondary: '#374151',    // Supporting gray
    accent: '#dbdbdb',       // Light borders
  },

  // Color Palette, Typography, Spacing, etc.
  // ...
}
```

### 2. Contract Header Design
```typescript
export const contractHeaderDesign = {
  container: { /* ... */ },
  companyName: { /* ... */ },
  organizationInfo: { /* ... */ },
  contractTitle: { /* ... */ },
}
```

### 3. Section Designs
```typescript
export const partyIdentityDesign = { /* ... */ }
export const workLocationTasksDesign = { /* ... */ }
export const employmentTermsDesign = { /* ... */ }
export const workTimeSalaryDesign = { /* ... */ }
// etc.
```

## Common Modifications

### Changing Colors
To change the primary color (currently black) to a blue:
```typescript
brand: {
  primary: '#1e40af',      // Change to blue
  secondary: '#374151',    // Keep supporting gray
  accent: '#dbdbdb',       // Keep light borders
},
```

### Adjusting Spacing
To create a more spacious layout:
```typescript
spacing: {
  micro: 2,       // Increase from 1
  tiny: 3,        // Increase from 2
  small: 6,       // Increase from 4
  // etc.
},
```

### Modifying Typography
To use larger text:
```typescript
typography: {
  // Font sizes
  sizes: {
    xs: 10,       // Increase from 9
    sm: 12,       // Increase from 11
    md: 14,       // Increase from 13
    // etc.
  },
}
```

## How the Design System is Applied

The design system is imported in `src/components/Meta/ContractPDFTemplate.tsx` and used to create styles:

```typescript
import { contractDesignSystem } from '@/lib/meta/design/contract-design-system.ts';

const styles = StyleSheet.create({
  page: contractDesignSystem.page.document,
  header: contractDesignSystem.contractHeader.container,
  // etc.
});
```

## Example Modifications

There are pre-made examples in `src/lib/meta/design/examples.md` that show:
- Corporate blue theme
- Larger, more spacious layout
- Custom color palettes (Forest Green, Ocean Blue, Sunset Orange)

## Testing Your Changes

After making changes:
1. Rebuild the project
2. Go to the Arbeidskontrakt Generator
3. Fill out the form and generate a PDF
4. Check that your changes appear as expected

This design system approach ensures all styling is centralized, making it easy to maintain a consistent look while allowing for customization.